body, html{
  position: relative;
  overflow-x: hidden;
}


html {
  --scrollbar-gutter: 0px;

  &.lenis-stopped {
    --scrollbar-gutter: var(--scrollbar-width);
  }
}

* {
  scrollbar-width: thin;
}

body {
  min-height: 100vh;
  overscroll-behavior: none;
  background-color: var(--color-primary);
  color: var(--color-secondary);
  display: flex;
  flex-direction: column;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Selection styling */
*::selection {
  background-color: var(--color-contrast);
  color: var(--color-primary);
}

/* SVG icon colors */
svg.icon {
  path[fill],
  rect[fill],
  circle[fill] {
    fill: currentColor;
  }
  path[stroke],
  rect[stroke],
  circle[stroke] {
    stroke: currentColor;
  }
}

/* Hover states */
.link {
  @media (--hover) {
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Focus states */
*:focus-visible {
  outline: 2px solid var(--color-contrast);
}

h1,
.h1 {
  font-size: clamp(4em, 17vw, 17vw);
  font-weight: 700;
  line-height: 0.75;
}

h2,
.h2 {
  font-size: clamp(1em, 7.5vw, 7.5vw);
  font-weight: 700;
  line-height: 0.8;
}

h3,
.h3 {
  font-size: clamp(1em, 2.56vw, 2.56vw);
  font-weight: 300;
  line-height: 1.1;
}

h4,
.h4 {
  font-size: 1.75em;
  font-weight: 300;
  line-height: 1.15;
}

h5,
.h5 {
  font-size: 1.25em;
  font-weight: 300;
  line-height: 1.2;
}

p,
.p {
  font-size: clamp(1em, 1vw, 1.5em);
  font-weight: 400;
  line-height: 1.4;
}

strong {
  font-weight: 700;
}

[data-hidden-on-init] {
  opacity: 0;
  will-change: opacity;
}

::view-transition-new(root) {
  z-index: 9999;
  animation: none !important;
}

::view-transition-group(root) {
  z-index: auto !important;
}

::view-transition-image-pair(root) {
  isolation: isolate;
  will-change: clip-path;
  z-index: 1;
}

::view-transition-old(root) {
  z-index: 1;
  animation: exit-old-view 1.25s both ease-in-out -0.25s !important;
}

@keyframes exit-old-view {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}


@keyframes accordion-down {
	from {
		height: 0;
	}
	to {
		height: var(--radix-accordion-content-height);
	}
}

@keyframes accordion-up {
	from {
		height: var(--radix-accordion-content-height);
	}
	to {
		height: 0;
	}
}

.variant--default {
  position: relative;
  display: inline-flex;
  font-weight: 600;
  align-items: center;
  gap: 0.25em;

  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    height: 2px;
    width: 100%;
    background: var(--color-contrast);
    transform: scaleX(1);
    transform-origin: left;
  }

  &:hover::after {
    animation: underline-disappear-reappear 0.6s var(--ease-in-out-expo) forwards;
  }
}

@keyframes underline-disappear-reappear {
  0% {
    transform: scaleX(1);
    transform-origin: right;
  }
  49.999% {
    transform: scaleX(0);
    transform-origin: right;
  }
  50% {
    transform: scaleX(0);
    transform-origin: left;
  }
  100% {
    transform: scaleX(1);
    transform-origin: left;
  }
}
